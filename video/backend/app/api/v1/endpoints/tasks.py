"""
任务管理API端点
"""

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.models.task import Task, Video
from app.services.task_service import TaskService
import json

router = APIRouter()


@router.get("/", response_model=List[dict])
async def get_tasks(db: Session = Depends(get_db)):
    """获取任务列表"""
    tasks = db.query(Task).order_by(Task.created_at.desc()).all()
    return [
        {
            "id": task.id,
            "name": task.name,
            "description": task.description,
            "status": task.status,
            "progress": task.progress,
            "created_at": task.created_at.isoformat(),
            "updated_at": task.updated_at.isoformat(),
            "video_count": len(task.videos)
        }
        for task in tasks
    ]


@router.post("/", response_model=dict)
async def create_task(
    name: str = Form(...),
    description: str = Form(""),
    config: str = Form("{}"),
    db: Session = Depends(get_db)
):
    """创建新任务"""
    try:
        config_dict = json.loads(config) if config else {}
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid config JSON")
    
    task = Task(
        name=name,
        description=description,
        config=config_dict,
        status="pending"
    )
    
    db.add(task)
    db.commit()
    db.refresh(task)
    
    return {
        "id": task.id,
        "name": task.name,
        "description": task.description,
        "status": task.status,
        "created_at": task.created_at.isoformat()
    }


@router.get("/{task_id}", response_model=dict)
async def get_task(task_id: int, db: Session = Depends(get_db)):
    """获取任务详情"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return {
        "id": task.id,
        "name": task.name,
        "description": task.description,
        "status": task.status,
        "progress": task.progress,
        "config": task.config,
        "created_at": task.created_at.isoformat(),
        "updated_at": task.updated_at.isoformat(),
        "videos": [
            {
                "id": video.id,
                "filename": video.filename,
                "original_filename": video.original_filename,
                "duration": video.duration,
                "status": video.status,
                "created_at": video.created_at.isoformat()
            }
            for video in task.videos
        ]
    }


@router.put("/{task_id}", response_model=dict)
async def update_task(
    task_id: int,
    name: str = None,
    description: str = None,
    status: str = None,
    db: Session = Depends(get_db)
):
    """更新任务"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    if name is not None:
        task.name = name
    if description is not None:
        task.description = description
    if status is not None:
        task.status = status
    
    db.commit()
    db.refresh(task)
    
    return {
        "id": task.id,
        "name": task.name,
        "description": task.description,
        "status": task.status,
        "updated_at": task.updated_at.isoformat()
    }


@router.delete("/{task_id}")
async def delete_task(task_id: int, db: Session = Depends(get_db)):
    """删除任务"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    db.delete(task)
    db.commit()
    
    return {"message": "Task deleted successfully"}


@router.post("/{task_id}/upload")
async def upload_video(
    task_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传视频到任务"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # 这里应该调用视频服务来处理文件上传
    # 暂时返回模拟响应
    return {
        "message": "Video uploaded successfully",
        "filename": file.filename,
        "size": file.size if hasattr(file, 'size') else 0
    }
